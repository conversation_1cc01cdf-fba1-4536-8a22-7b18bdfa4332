import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { DatabaseService } from './services/DatabaseService.js';
import { setupRoutes } from './routes/index.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet({
  contentSecurityPolicy: false, // Allow frontend to load
  crossOriginEmbedderPolicy: false
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs
  standardHeaders: true,
  legacyHeaders: false,
});
app.use(limiter);

// CORS configuration
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));

// Body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files from the frontend build
app.use(express.static(join(__dirname, '../dist')));

// Initialize database
let dbService;
try {
  dbService = new DatabaseService();
  await dbService.initialize();
} catch (error) {
  process.exit(1);
}

// Setup API routes
setupRoutes(app, dbService);

// Catch-all handler for React app
app.get('*', (req, res) => {
  res.sendFile(join(__dirname, '../dist/index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
  res.status(500).json({ error: 'Internal server error' });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  if (dbService) {
    await dbService.close();
  }
  process.exit(0);
});

process.on('SIGINT', async () => {
  if (dbService) {
    await dbService.close();
  }
  process.exit(0);
});

app.listen(PORT, () => {
  console.log(`LifeTracker server running on port ${PORT}`);
});