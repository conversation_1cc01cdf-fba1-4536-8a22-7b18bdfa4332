import { Router } from 'express';
import { body, param, validationResult } from 'express-validator';

export function setupRoutes(app, dbService) {
  const router = Router();

  // Validation middleware
  const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ error: 'Invalid input data' });
    }
    next();
  };

  // Input sanitization
  const sanitizeTaskInput = [
    body('title').trim().isLength({ min: 1, max: 500 }).escape(),
    body('description').optional().trim().isLength({ max: 2000 }).escape(),
    body('dueDate').isISO8601().toDate(),
    body('dueTime').matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    body('priority').isIn(['Low', 'Medium', 'High']),
    body('category').optional().trim().isLength({ max: 100 }).escape(),
    body('isRecurring').isBoolean(),
    body('recurrenceType').optional().isIn(['Daily', 'Weekly', 'Monthly', 'Yearly']),
    body('recurrenceInterval').optional().isInt({ min: 1, max: 365 })
  ];

  // GET /api/tasks - Get all tasks
  router.get('/tasks', async (req, res) => {
    try {
      const tasks = await dbService.getTasks();
      res.json({ tasks });
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch tasks' });
    }
  });

  // POST /api/tasks - Create new task
  router.post('/tasks', sanitizeTaskInput, handleValidationErrors, async (req, res) => {
    try {
      const taskData = {
        ...req.body,
        id: crypto.randomUUID(),
        isCompleted: false,
        completedAt: null,
        createdAt: new Date().toISOString(),
        dueDate: req.body.dueDate.toISOString().split('T')[0] // Convert to YYYY-MM-DD
      };
      
      const task = await dbService.addTask(taskData);
      res.status(201).json({ task });
    } catch (error) {
      res.status(500).json({ error: 'Failed to create task' });
    }
  });

  // PUT /api/tasks/:id - Update task
  router.put('/tasks/:id', 
    param('id').isUUID(),
    sanitizeTaskInput,
    body('isCompleted').isBoolean(),
    body('completedAt').optional().isISO8601(),
    handleValidationErrors, 
    async (req, res) => {
      try {
        const taskData = {
          ...req.body,
          id: req.params.id,
          dueDate: req.body.dueDate.toISOString().split('T')[0]
        };
        
        const task = await dbService.updateTask(taskData);
        res.json({ task });
      } catch (error) {
        res.status(500).json({ error: 'Failed to update task' });
      }
    }
  );

  // DELETE /api/tasks/:id - Delete task (permanent deletion)
  router.delete('/tasks/:id', param('id').isUUID(), handleValidationErrors, async (req, res) => {
    try {
      await dbService.deleteTask(req.params.id);
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ error: 'Failed to delete task' });
    }
  });

  // GET /api/settings - Get settings
  router.get('/settings', async (req, res) => {
    try {
      const settings = await dbService.getSettings();
      res.json({ settings });
    } catch (error) {
      res.status(500).json({ error: 'Failed to fetch settings' });
    }
  });

  // PUT /api/settings/:key - Update setting
  router.put('/settings/:key', 
    param('key').isIn(['showCompleted', 'seeded']),
    body('value').isBoolean(),
    handleValidationErrors,
    async (req, res) => {
      try {
        await dbService.updateSetting(req.params.key, req.body.value);
        res.json({ success: true });
      } catch (error) {
        res.status(500).json({ error: 'Failed to update setting' });
      }
    }
  );

  // POST /api/seed - Seed initial tasks
  router.post('/seed', 
    body('tasks').isArray({ min: 0, max: 50 }),
    body('tasks.*.title').trim().isLength({ min: 1, max: 500 }),
    body('tasks.*.dueDate').isISO8601(),
    body('tasks.*.dueTime').matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/),
    body('tasks.*.priority').isIn(['Low', 'Medium', 'High']),
    handleValidationErrors,
    async (req, res) => {
      try {
        await dbService.seedInitialTasks(req.body.tasks);
        res.json({ success: true });
      } catch (error) {
        res.status(500).json({ error: 'Failed to seed tasks' });
      }
    }
  );

  // Health check endpoint
  router.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
  });

  app.use('/api', router);
}