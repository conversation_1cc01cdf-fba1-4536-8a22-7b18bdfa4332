import React, { createContext, ReactNode, useEffect, useState, useCallback } from 'react';
import { Settings } from '../types';
import { dbService } from '../services/dbService';

interface SettingsContextType {
    showCompleted: boolean;
    setShowCompleted: (show: boolean) => void;
}

export const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const SettingsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [settings, setSettings] = useState<Settings>({ showCompleted: true, seeded: false });

    const loadSettings = useCallback(async () => {
        const dbSettings = await dbService.getSettings();
        setSettings(dbSettings);
    }, []);

    useEffect(() => {
        if(dbService.isInitialized) {
            loadSettings();
        }
    }, [loadSettings]);

    const setShowCompleted = useCallback(async (show: boolean) => {
        setSettings(s => ({ ...s, showCompleted: show }));
        await dbService.updateSetting('showCompleted', show);
    }, []);

    const value = {
        showCompleted: settings.showCompleted,
        setShowCompleted
    };

    return (
        <SettingsContext.Provider value={value}>
            {children}
        </SettingsContext.Provider>
    );
};
