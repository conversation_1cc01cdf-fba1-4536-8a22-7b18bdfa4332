import React, { createContext, ReactNode, useEffect, useState, useCallback } from 'react';
import { Settings } from '../types';
import { apiService } from '../services/apiService';

interface SettingsContextType {
    showCompleted: boolean;
    setShowCompleted: (show: boolean) => void;
}

export const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const SettingsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [settings, setSettings] = useState<Settings>({ showCompleted: true, seeded: false });

    const loadSettings = useCallback(async () => {
        const dbSettings = await apiService.getSettings();
        setSettings(dbSettings);
    }, []);

    useEffect(() => {
        if(apiService.isInitialized) {
            loadSettings();
        }
    }, [loadSettings]);

    const setShowCompleted = useCallback(async (show: boolean) => {
        setSettings(s => ({ ...s, showCompleted: show }));
        await apiService.updateSetting('showCompleted', show);
    }, []);

    const value = {
        showCompleted: settings.showCompleted,
        setShowCompleted
    };

    return (
        <SettingsContext.Provider value={value}>
            {children}
        </SettingsContext.Provider>
    );
};
