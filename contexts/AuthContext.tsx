
import React, { createContext, useState, useCallback, useEffect, ReactNode } from 'react';
import { dbService } from '../services/dbService';

interface AuthContextType {
    isAuthenticated: boolean;
    isInitializing: boolean;
    login: (username: string, password: string) => Promise<void>;
    logout: () => void;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
    const [isInitializing, setIsInitializing] = useState<boolean>(true);

    useEffect(() => {
        const storedAuth = localStorage.getItem('isAuthenticated');
        if (storedAuth === 'true') {
            setIsAuthenticated(true);
        }
        setIsInitializing(false);
    }, []);
    
    const login = useCallback(async (username: string, password: string): Promise<void> => {
        return new Promise((resolve, reject) => {
            setTimeout(() => {
                if (username === 'scharway' && password === 'Lookup88?') {
                    setIsAuthenticated(true);
                    localStorage.setItem('isAuthenticated', 'true');
                    resolve();
                } else {
                    reject(new Error('Invalid username or password. Please check your credentials.'));
                }
            }, 500);
        });
    }, []);

    const logout = useCallback(() => {
        setIsAuthenticated(false);
        localStorage.removeItem('isAuthenticated');
        dbService.clearDatabase();
    }, []);

    const value = { isAuthenticated, isInitializing, login, logout };

    return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
