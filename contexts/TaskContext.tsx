import React, { createContext, ReactNode, useState, useEffect, useCallback } from 'react';
import { Task, Priority, RecurrenceType, TaskContextType } from '../types';
import { getNextRecurrenceDate } from '../utils/dateUtils';
import { dbService } from '../services/dbService';

export const TaskContext = createContext<TaskContextType | undefined>(undefined);

const initialSeedTasks: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>[] = [
    {
        title: "Renew Gym Membership",
        dueDate: "2024-08-15",
        dueTime: "10:00",
        isRecurring: true,
        recurrenceType: RecurrenceType.Yearly,
        recurrenceInterval: 1,
        category: 'Health',
        priority: Priority.Medium,
    },
    {
        title: "Monthly Project Report",
        dueDate: "2024-09-05",
        dueTime: "17:00",
        isRecurring: true,
        recurrenceType: RecurrenceType.Monthly,
        recurrenceInterval: 1,
        category: 'Work',
        priority: Priority.High,
    }
];


export const TaskProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [tasks, setTasks] = useState<Task[]>([]);
    const [isSeeding, setIsSeeding] = useState(true);

    const loadAndSeedTasks = useCallback(async () => {
        setIsSeeding(true);
        await dbService.seedInitialTasks(initialSeedTasks);
        const dbTasks = await dbService.getTasks();
        setTasks(dbTasks);
        setIsSeeding(false);
    }, []);

    useEffect(() => {
        if (dbService.isInitialized) {
            loadAndSeedTasks();
        }
    }, [loadAndSeedTasks]);

    const refreshTasks = async () => {
        const updatedTasks = await dbService.getTasks();
        setTasks(updatedTasks);
    };

    const addTask = async (task: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>) => {
        await dbService.addTask(task);
        await refreshTasks();
    };
    
    const updateTask = async (updatedTask: Task) => {
        if (updatedTask.isRecurring && updatedTask.isCompleted) {
            const originalTask = tasks.find(t => t.id === updatedTask.id);
            if(originalTask) {
                // Use a transaction to ensure both operations succeed or fail together
                await dbService.runInTransaction(async () => {
                    // 1. Update the original recurring task for its next occurrence
                    const nextDueDate = getNextRecurrenceDate(originalTask);
                    await dbService.updateTask({ ...originalTask, dueDate: nextDueDate });
                    
                    // 2. Create a new, non-recurring, completed task instance
                    const completedInstance: Task = {
                        ...originalTask,
                        id: crypto.randomUUID(),
                        isRecurring: false,
                        isCompleted: true,
                        completedAt: new Date().toISOString(),
                        createdAt: new Date().toISOString(), 
                    };
                    await dbService.addFullTask(completedInstance);
                });
            }
        } else {
            // Standard update for non-recurring tasks or other field changes
            const taskToSave = { ...updatedTask, completedAt: updatedTask.isCompleted ? new Date().toISOString() : undefined };
            await dbService.updateTask(taskToSave);
        }
        await refreshTasks();
    };

    const deleteTask = async (taskId: string) => {
        await dbService.deleteTask(taskId);
        await refreshTasks();
    };
    
    const value = { tasks, addTask, updateTask, deleteTask, isSeeding };

    return (
        <TaskContext.Provider value={value}>
            {children}
        </TaskContext.Provider>
    );
};