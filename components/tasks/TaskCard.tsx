import React, { useState, useContext } from 'react';
import { Task, Priority } from '../../types';
import { TaskContext } from '../../contexts/TaskContext';
import TaskFormModal from './TaskFormModal';
import { formatRelativeDate, formatCompletedDate } from '../../utils/dateUtils';
import { CalendarIcon, PencilIcon, TrashIcon, ArrowPathIcon } from '../icons/Icons';

interface TaskCardProps {
    task: Task;
}

const priorityClasses = {
    [Priority.High]: 'bg-rose-500',
    [Priority.Medium]: 'bg-brand-secondary',
    [Priority.Low]: 'bg-brand-border',
};

const TaskCard: React.FC<TaskCardProps> = ({ task }) => {
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const taskContext = useContext(TaskContext);

    if (!taskContext) throw new Error("TaskContext not found");
    const { updateTask, deleteTask } = taskContext;

    const handleToggleComplete = () => {
        updateTask({ ...task, isCompleted: !task.isCompleted });
    };
    
    const handleDelete = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (window.confirm("Are you sure you want to permanently delete this task? This action cannot be undone.")) {
            deleteTask(task.id);
        }
    };

    const handleEdit = (e: React.MouseEvent) => {
        e.stopPropagation();
        setIsEditModalOpen(true);
    }
    
    const { text: relativeDateText, isOverdue } = task.isCompleted && task.completedAt 
        ? { text: formatCompletedDate(task.completedAt), isOverdue: false } 
        : formatRelativeDate(task.dueDate, task.dueTime);

    const dueDateTime = new Date(`${task.dueDate}T${task.dueTime}`);
    const absoluteDateString = dueDateTime.toLocaleDateString(undefined, {
        month: 'short',
        day: 'numeric',
        year: dueDateTime.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined,
    });
    const absoluteTimeString = dueDateTime.toLocaleTimeString(undefined, {
        hour: 'numeric',
        minute: '2-digit',
    });
    const fullAbsoluteDateTimeString = `${absoluteDateString} at ${absoluteTimeString}`;

    const cardClasses = `
        relative group flex items-start space-x-4 bg-white rounded-lg p-4 border border-brand-border
        hover:border-brand-secondary/30 hover:bg-white
        transition-all duration-300 transform
        ${task.isCompleted ? 'opacity-60 completed' : 'hover:scale-[1.02] hover:shadow-md hover:shadow-brand-bg-alt/50'}
    `;

    return (
        <>
            <div 
                className={cardClasses}
            >
                <div className={`absolute left-0 top-0 bottom-0 w-1.5 rounded-l-lg ${priorityClasses[task.priority]}`}></div>
                <div className="pl-2 flex-shrink-0">
                     <input
                        type="checkbox"
                        checked={task.isCompleted}
                        onChange={handleToggleComplete}
                        onClick={(e) => e.stopPropagation()}
                        className="h-5 w-5 mt-0.5 rounded-full border-zinc-300 bg-transparent text-brand-secondary focus:ring-brand-secondary focus:ring-offset-white cursor-pointer"
                        aria-label={`Mark task "${task.title}" as ${task.isCompleted ? 'incomplete' : 'complete'}`}
                    />
                </div>
                <div className="flex-1 min-w-0" onClick={handleEdit}>
                    <p className={`text-lg font-medium text-brand-primary truncate strikethrough-animated ${task.isCompleted ? 'text-brand-secondary' : ''}`}>
                        {task.title}
                    </p>
                    
                    <div className="mt-1 space-y-0.5">
                        <div className={`flex items-center text-sm ${
                            task.isCompleted ? 'text-green-600' : 
                            isOverdue ? 'text-rose-600 font-medium' : 'text-brand-secondary'
                        }`}>
                            <CalendarIcon className="w-4 h-4 mr-1.5 flex-shrink-0" />
                            <span>{relativeDateText}</span>
                        </div>
                        {!task.isCompleted && (
                             <p className="text-xs text-brand-secondary pl-[22px]">
                                {fullAbsoluteDateTimeString}
                            </p>
                        )}
                    </div>

                    {(task.isRecurring || task.category) && (
                        <div className="mt-2 flex items-center space-x-3 text-xs text-brand-secondary">
                            {task.isRecurring && !task.isCompleted && (
                                <div className="flex items-center">
                                    <ArrowPathIcon className="w-4 h-4 mr-1" />
                                    <span>{task.recurrenceInterval === 1 ? task.recurrenceType : `Every ${task.recurrenceInterval} ${task.recurrenceType?.toLowerCase().slice(0, -1)}s`}</span>
                                </div>
                            )}
                            {task.category && (
                                <span className="px-2 py-0.5 bg-brand-bg-alt text-brand-secondary rounded-full font-medium">{task.category}</span>
                            )}
                        </div>
                    )}
                </div>

                <div className={`absolute top-3 right-3 flex items-center space-x-1 transition-opacity duration-200 group-hover:opacity-100 ${isEditModalOpen ? 'opacity-100' : 'opacity-0'}`}>
                    <button 
                        onClick={handleEdit}
                        className="p-1.5 text-brand-secondary hover:text-brand-primary hover:bg-brand-bg-alt rounded-md focus:outline-none focus:ring-2 focus:ring-brand-secondary"
                        aria-label="Edit task"
                    >
                        <PencilIcon className="w-5 h-5" />
                    </button>
                    <button 
                        onClick={handleDelete}
                        className="p-1.5 text-brand-secondary hover:text-brand-primary hover:bg-brand-bg-alt rounded-md focus:outline-none focus:ring-2 focus:ring-brand-secondary"
                        aria-label="Delete task"
                    >
                        <TrashIcon className="w-5 h-5" />
                    </button>
                </div>
            </div>

            {isEditModalOpen && (
                <TaskFormModal
                    isOpen={isEditModalOpen}
                    onClose={() => setIsEditModalOpen(false)}
                    taskToEdit={task}
                />
            )}
        </>
    );
};

export default TaskCard;