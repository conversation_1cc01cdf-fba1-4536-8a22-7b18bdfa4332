import React from 'react';
import { XMarkIcon } from '../icons/Icons';
import SearchInput from '../ui/SearchInput';
import DateTimeDisplay from './DateTimeDisplay';
import { SettingsContext } from '../../contexts/SettingsContext';

interface TasksHeaderProps {
    selectedDate: Date | null;
    setSelectedDate: (date: Date | null) => void;
    searchQuery: string;
    setSearchQuery: (query: string) => void;
    sortBy: string;
    setSortBy: (sort: string) => void;
}

const TasksHeader: React.FC<TasksHeaderProps> = ({ selectedDate, setSelectedDate, searchQuery, setSearchQuery, sortBy, setSortBy }) => {
    const settingsContext = React.useContext(SettingsContext);
    if (!settingsContext) {
        throw new Error('TasksHeader must be used within a SettingsProvider');
    }
    const { showCompleted, setShowCompleted } = settingsContext;
    
    const formattedSelectedDate = selectedDate
        ? selectedDate.toLocaleDateString(undefined, {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          })
        : '';
        
    const TitleDisplay = () => {
        if (selectedDate) {
            return (
                <div>
                    <h1 className="text-xl font-bold text-brand-primary">
                        Tasks for {formattedSelectedDate}
                    </h1>
                     <button onClick={() => setSelectedDate(null)} className="mt-1 flex items-center text-sm text-brand-secondary hover:text-brand-primary hover:underline">
                        <XMarkIcon className="w-4 h-4 mr-1"/>
                        Show All Tasks
                    </button>
                </div>
            )
        }
        
        return (
            <div>
                <h1 className="text-3xl font-bold text-brand-primary">My Tasks</h1>
                 <DateTimeDisplay />
            </div>
        )
    };

    return (
        <div className="space-y-4">
            <TitleDisplay />
            <div className="flex flex-col md:flex-row items-center gap-4">
                <div className="w-full md:flex-1">
                    <SearchInput value={searchQuery} onChange={e => setSearchQuery(e.target.value)} />
                </div>
                <div className="w-full md:w-auto flex items-center gap-4">
                    <select
                        id="sort-by"
                        value={sortBy}
                        onChange={(e) => setSortBy(e.target.value)}
                        className="w-full md:w-auto h-11 pl-3 pr-8 text-base bg-white border border-brand-border rounded-md focus:outline-none focus:ring-2 focus:ring-brand-secondary text-sm appearance-none"
                        aria-label="Sort tasks by"
                    >
                        <option value="due-date">Sort by Due Date</option>
                        <option value="priority">Sort by Priority</option>
                        <option value="title-asc">Sort by Title (A-Z)</option>
                        <option value="created-desc">Sort by Newest</option>
                    </select>
                    <label className="flex items-center space-x-2 cursor-pointer text-sm text-brand-secondary whitespace-nowrap">
                        <input
                            type="checkbox"
                            checked={showCompleted}
                            onChange={(e) => setShowCompleted(e.target.checked)}
                            className="h-4 w-4 rounded border-brand-border bg-brand-bg-alt text-brand-secondary focus:ring-brand-secondary focus:ring-offset-gray-50"
                        />
                        <span>Show Completed</span>
                    </label>
                </div>
            </div>
        </div>
    );
};

export default TasksHeader;