import React, { useContext, useMemo } from 'react';
import { TaskContext } from '../../contexts/TaskContext';
import { SettingsContext } from '../../contexts/SettingsContext';
import TaskCard from './TaskCard';
import { Task, Priority } from '../../types';
import { DocumentMagnifyingGlassIcon } from '../icons/Icons';

const priorityOrder = {
    [Priority.High]: 1,
    [Priority.Medium]: 2,
    [Priority.Low]: 3,
};

interface TaskListProps {
    sortBy: string;
    selectedDate: Date | null;
    searchQuery: string;
}

const TaskList: React.FC<TaskListProps> = ({ sortBy, selectedDate, searchQuery }) => {
    const taskContext = useContext(TaskContext);
    const settingsContext = useContext(SettingsContext);

    if (!taskContext || !settingsContext) {
        throw new Error('TaskList must be used within Task and Settings providers');
    }

    const { tasks } = taskContext;
    const { showCompleted } = settingsContext;

    const sortedAndFilteredTasks = useMemo(() => {
        let filtered = tasks
            .filter(task => showCompleted || !task.isCompleted)
            .filter(task => {
                if (!searchQuery) return true;
                const query = searchQuery.toLowerCase();
                return task.title.toLowerCase().includes(query) || 
                       (task.description && task.description.toLowerCase().includes(query));
            });

        // Filter by selected date
        if (selectedDate) {
            const dateString = selectedDate.toISOString().split('T')[0];
            filtered = filtered.filter(task => task.dueDate === dateString);
        }
        
        const getFullDueDate = (task: Task) => new Date(`${task.dueDate}T${task.dueTime}`);

        return filtered.sort((a, b) => {
            // 1. Incomplete tasks always come before completed tasks
            if (a.isCompleted !== b.isCompleted) {
                return a.isCompleted ? 1 : -1;
            }

            // 2. Apply selected sort logic
            switch (sortBy) {
                case 'priority':
                    if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
                        return priorityOrder[a.priority] - priorityOrder[b.priority];
                    }
                    return getFullDueDate(a).getTime() - getFullDueDate(b).getTime();

                case 'title-asc':
                    return a.title.localeCompare(b.title);

                case 'created-desc':
                    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
                
                case 'due-date':
                default:
                    const aDueDate = getFullDueDate(a);
                    const bDueDate = getFullDueDate(b);
                    if (aDueDate.getTime() !== bDueDate.getTime()) {
                        return aDueDate.getTime() - bDueDate.getTime();
                    }
                    return priorityOrder[a.priority] - priorityOrder[b.priority];
            }
        });
    }, [tasks, showCompleted, sortBy, selectedDate, searchQuery]);
    

    return (
        <div>
            {sortedAndFilteredTasks.length === 0 ? (
                <div className="text-center py-20 px-6 bg-gray-50 rounded-lg border border-dashed border-brand-border">
                    <DocumentMagnifyingGlassIcon className="mx-auto h-16 w-16 text-brand-border" />
                    <h3 className="mt-4 text-xl font-semibold text-brand-primary">
                        {searchQuery || selectedDate ? "No tasks match" : "No tasks here"}
                    </h3>
                    <p className="mt-2 text-sm text-brand-secondary">
                        {searchQuery || selectedDate ? "Try adjusting your search or date selection." : "Ready to get organized? Add your first task."}
                    </p>
                </div>
            ) : (
                <div className="space-y-3">
                    {sortedAndFilteredTasks.map(task => (
                        <TaskCard key={task.id} task={task} />
                    ))}
                </div>
            )}
        </div>
    );
};

export default TaskList;