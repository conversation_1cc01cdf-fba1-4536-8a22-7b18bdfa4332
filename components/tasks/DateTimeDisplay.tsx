import React, { useState, useEffect } from 'react';

const DateTimeDisplay: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    // Update the time every minute
    const timerId = setInterval(() => setCurrentTime(new Date()), 1000 * 60);
    // Cleanup interval on component unmount
    return () => clearInterval(timerId);
  }, []);

  const formattedDate = currentTime.toLocaleDateString(undefined, {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  const formattedTime = currentTime.toLocaleTimeString(undefined, {
    hour: 'numeric',
    minute: '2-digit',
  });

  return (
    <p className="mt-1 text-sm text-brand-secondary" aria-live="polite">
      {formattedDate} &bull; {formattedTime}
    </p>
  );
};

export default DateTimeDisplay;