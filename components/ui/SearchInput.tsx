import React from 'react';
import { MagnifyingGlassIcon } from '../icons/Icons';

interface SearchInputProps {
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    placeholder?: string;
}

const SearchInput: React.FC<SearchInputProps> = ({ value, onChange, placeholder = "Search tasks..." }) => {
    return (
        <div className="relative w-full">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <MagnifyingGlassIcon className="h-5 w-5 text-brand-secondary" aria-hidden="true" />
            </div>
            <input
                type="search"
                name="search"
                id="search"
                className="block w-full h-11 rounded-md border-0 py-2 pl-10 text-brand-primary bg-brand-bg-alt ring-0 placeholder:text-brand-secondary focus:ring-2 focus:ring-inset focus:ring-brand-secondary sm:text-sm transition"
                placeholder={placeholder}
                value={value}
                onChange={onChange}
            />
        </div>
    );
};

export default SearchInput;