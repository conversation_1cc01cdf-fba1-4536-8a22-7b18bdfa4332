import React, { useState, useContext } from 'react';
import Sidebar from './Sidebar';
import TasksView from '../../views/TasksView';
import SettingsView from '../../views/SettingsView';
import ProfileView from '../../views/ProfileView';
import { View } from '../../types';
import { TaskContext } from '../../contexts/TaskContext';
import Header from './Header';

const MainLayout: React.FC = () => {
    const [currentView, setCurrentView] = useState<View>('tasks');
    const taskContext = useContext(TaskContext);

    if (!taskContext) throw new Error("TaskContext not found");

    const { isSeeding } = taskContext;

    const renderView = () => {
        switch (currentView) {
            case 'tasks':
                return <TasksView />;
            case 'settings':
                return <SettingsView />;
            case 'profile':
                return <ProfileView />;
            default:
                return <TasksView />;
        }
    };
    
    if (isSeeding) {
        return (
            <div className="flex items-center justify-center h-screen bg-gray-50">
                <div className="flex flex-col items-center">
                    <div className="w-12 h-12 border-4 border-brand-secondary border-t-transparent rounded-full animate-spin"></div>
                    <p className="mt-4 text-brand-secondary">Loading your tasks...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="flex h-screen bg-gray-50 text-brand-primary">
            <Sidebar currentView={currentView} setCurrentView={setCurrentView} />
            <div className="flex-1 flex flex-col overflow-hidden">
                <Header 
                    currentView={currentView} 
                />
                <main className="flex-1 overflow-y-auto custom-scrollbar bg-gray-50">
                    <div className="p-4 sm:p-6 lg:p-8">
                        {renderView()}
                    </div>
                </main>
            </div>
        </div>
    );
};

export default MainLayout;