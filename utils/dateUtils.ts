import { Task, RecurrenceType } from '../types';

export function formatRelativeDate(dueDate: string, dueTime: string): { text: string; isOverdue: boolean } {
    const now = new Date();
    const dueDateTime = new Date(`${dueDate}T${dueTime}`);

    const isTaskOverdue = now > dueDateTime;

    const timeString = dueDateTime.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });

    // For day-based comparisons (Today, Tomorrow, Overdue by X days)
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const dueDay = new Date(dueDate);
    // Adjust for timezone offset to treat date as local when creating from YYYY-MM-DD string
    dueDay.setMinutes(dueDay.getMinutes() + dueDay.getTimezoneOffset());
    dueDay.setHours(0, 0, 0, 0);

    if (isTaskOverdue) {
        const overdueDiffTime = today.getTime() - dueDay.getTime();
        const overdueDiffDays = Math.ceil(overdueDiffTime / (1000 * 60 * 60 * 24));
        
        if (overdueDiffDays > 0) {
            return { text: `Overdue by ${overdueDiffDays} day${overdueDiffDays > 1 ? 's' : ''}`, isOverdue: true };
        } else {
            // Was due earlier today
            return { text: `Overdue since ${timeString}`, isOverdue: true };
        }
    }

    const diffTime = dueDay.getTime() - today.getTime();
    const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
        return { text: `Due Today at ${timeString}`, isOverdue: false };
    }
    if (diffDays === 1) {
        return { text: `Due Tomorrow at ${timeString}`, isOverdue: false };
    }

    // For dates further in the future, show the actual date.
    const dateString = dueDateTime.toLocaleDateString(undefined, {
        month: 'short',
        day: 'numeric',
        year: dueDateTime.getFullYear() !== today.getFullYear() ? 'numeric' : undefined,
    });

    return { text: `Due ${dateString} at ${timeString}`, isOverdue: false };
}

export function formatCompletedDate(completedAt: string): string {
    const date = new Date(completedAt);
    return `Completed ${date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}`;
}

export function getNextRecurrenceDate(task: Task): string {
    const { dueDate, recurrenceType, recurrenceInterval = 1 } = task;
    const currentDueDate = new Date(dueDate);
    currentDueDate.setMinutes(currentDueDate.getMinutes() + currentDueDate.getTimezoneOffset());
    
    switch (recurrenceType) {
        case RecurrenceType.Daily:
            currentDueDate.setDate(currentDueDate.getDate() + recurrenceInterval);
            break;
        case RecurrenceType.Weekly:
            currentDueDate.setDate(currentDueDate.getDate() + 7 * recurrenceInterval);
            break;
        case RecurrenceType.Monthly:
            currentDueDate.setMonth(currentDueDate.getMonth() + recurrenceInterval);
            break;
        case RecurrenceType.Yearly:
            currentDueDate.setFullYear(currentDueDate.getFullYear() + recurrenceInterval);
            break;
        default:
            // Should not happen, but return a sensible default
            currentDueDate.setDate(currentDueDate.getDate() + 1);
    }
    return currentDueDate.toISOString().split('T')[0];
}
