
import initSqlJs from 'sql.js';
import { Task, Settings, Priority, RecurrenceType } from '../types';

let db: initSqlJs.Database | null = null;
let SQL: initSqlJs.SqlJsStatic;

const DB_STORAGE_KEY = 'lifetracker_sqlite_db';

async function initDB() {
    if (db) return db;

    try {
        // Fetch the wasm binary directly to avoid file system errors in certain environments
        const wasmResponse = await fetch('https://cdn.jsdelivr.net/npm/sql.js@1.13.0/dist/sql-wasm.wasm');
        const wasmBinary = await wasmResponse.arrayBuffer();
        SQL = await initSqlJs({ wasmBinary });

        const storedDb = localStorage.getItem(DB_STORAGE_KEY);
        if (storedDb) {
            const dbArray = new Uint8Array(JSON.parse(storedDb));
            db = new SQL.Database(dbArray);
        } else {
            db = new SQL.Database();
            createSchema();
        }
    } catch (e) {
        console.error("Failed to initialize database:", e);
        // Fallback or error handling
    }

    return db;
}

function saveDB() {
    if (db) {
        try {
            const data = db.export();
            localStorage.setItem(DB_STORAGE_KEY, JSON.stringify(Array.from(data)));
        } catch (e) {
            console.error("Failed to save database:", e);
        }
    }
}

function createSchema() {
    if (!db) return;

    const schema = `
        CREATE TABLE IF NOT EXISTS tasks (
            id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            description TEXT,
            dueDate TEXT NOT NULL,
            dueTime TEXT NOT NULL,
            isCompleted INTEGER NOT NULL,
            completedAt TEXT,
            priority TEXT NOT NULL,
            category TEXT,
            isRecurring INTEGER NOT NULL,
            recurrenceType TEXT,
            recurrenceInterval INTEGER,
            createdAt TEXT NOT NULL
        );

        CREATE TABLE IF NOT EXISTS settings (
            key TEXT PRIMARY KEY,
            value TEXT NOT NULL
        );
    `;
    db.exec(schema);

    db.run("INSERT OR IGNORE INTO settings (key, value) VALUES ('showCompleted', 'true'), ('seeded', 'false');");
    
    saveDB();
}

function rowToTask(row: (string | number | null)[]): Task {
    return {
        id: row[0] as string,
        title: row[1] as string,
        description: row[2] as string | undefined,
        dueDate: row[3] as string,
        dueTime: row[4] as string,
        isCompleted: row[5] === 1,
        completedAt: row[6] as string | undefined,
        priority: row[7] as Priority,
        category: row[8] as string | undefined,
        isRecurring: row[9] === 1,
        recurrenceType: row[10] as RecurrenceType | undefined,
        recurrenceInterval: row[11] as number | undefined,
        createdAt: row[12] as string,
    };
}


export const dbService = {
    isInitialized: false,
    _inTransaction: false,

    async initialize(): Promise<void> {
        if(this.isInitialized) return;
        await initDB();
        this.isInitialized = true;
    },

    clearDatabase(): void {
        localStorage.removeItem(DB_STORAGE_KEY);
        db = null;
        this.isInitialized = false;
    },

    async runInTransaction(callback: () => Promise<void>): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for transaction.");
        this._inTransaction = true;
        db.exec("BEGIN TRANSACTION;");
        try {
            await callback();
            db.exec("COMMIT;");
        } catch (e) {
            console.error("Transaction failed, rolling back", e);
            db.exec("ROLLBACK;");
            throw e; // re-throw the error
        } finally {
            this._inTransaction = false;
            saveDB();
        }
    },

    async getSettings(): Promise<Settings> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for getSettings.");
        const res = db.exec("SELECT key, value FROM settings");
        const settings: Partial<Settings> = {
            showCompleted: true,
            seeded: false,
        };
        if (res[0]) {
            res[0].values.forEach(([key, value]) => {
                const k = key as keyof Settings;
                if (k === 'showCompleted' || k === 'seeded') {
                    settings[k] = value === 'true';
                }
            });
        }
        return settings as Settings;
    },

    async updateSetting(key: keyof Settings, value: string | boolean): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for updateSetting.");
        db.run("INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)", [key, String(value)]);
        if (!this._inTransaction) saveDB();
    },

    async getTasks(): Promise<Task[]> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for getTasks.");
        const res = db.exec("SELECT * FROM tasks");
        if (res.length === 0 || !res[0]?.values) {
            return [];
        }
        return res[0].values.map(row => rowToTask(row as any[]));
    },

    async addFullTask(task: Task): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for addFullTask.");
        const sql = `INSERT INTO tasks (id, title, description, dueDate, dueTime, isCompleted, completedAt, priority, category, isRecurring, recurrenceType, recurrenceInterval, createdAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
        const params = [
            task.id, task.title, task.description ?? null, task.dueDate, task.dueTime,
            task.isCompleted ? 1 : 0, task.completedAt ?? null, task.priority, task.category ?? null,
            task.isRecurring ? 1 : 0, task.recurrenceType ?? null, task.recurrenceInterval ?? null, task.createdAt
        ];
        db.run(sql, params);
        if (!this._inTransaction) saveDB();
    },

    async addTask(task: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>): Promise<Task> {
        const newTask: Task = {
            ...task,
            id: crypto.randomUUID(),
            isCompleted: false,
            completedAt: undefined,
            createdAt: new Date().toISOString(),
        };
        await this.addFullTask(newTask);
        return newTask;
    },
    
    async updateTask(task: Task): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for updateTask.");
        const sql = `UPDATE tasks SET title = ?, description = ?, dueDate = ?, dueTime = ?, isCompleted = ?, completedAt = ?, priority = ?, category = ?, isRecurring = ?, recurrenceType = ?, recurrenceInterval = ? WHERE id = ?`;
        const params = [
            task.title, task.description ?? null, task.dueDate, task.dueTime,
            task.isCompleted ? 1 : 0, task.completedAt ?? null, task.priority, task.category ?? null,
            task.isRecurring ? 1 : 0, task.recurrenceType ?? null, task.recurrenceInterval ?? null,
            task.id
        ];
        db.run(sql, params);
        if (!this._inTransaction) saveDB();
    },

    async deleteTask(taskId: string): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for deleteTask.");
        db.run("DELETE FROM tasks WHERE id = ?", [taskId]);
        if (!this._inTransaction) saveDB();
    },
    
    async seedInitialTasks(tasksToSeed: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>[]): Promise<void> {
        await this.initialize();
        if (!db) throw new Error("Database not initialized for seeding.");
        const settings = await this.getSettings();
        if (settings.seeded) return;
        
        const tasks = await this.getTasks();
        if (tasks.length > 0) {
             await this.updateSetting('seeded', true);
             return;
        }

        await this.runInTransaction(async () => {
            for (const task of tasksToSeed) {
                await this.addTask(task);
            }
            await this.updateSetting('seeded', true);
        });
    }
};
