import { Task, Settings, Priority, RecurrenceType } from '../types';

const API_BASE_URL = '/api';

class ApiError extends Error {
  constructor(message: string, public status: number) {
    super(message);
    this.name = 'ApiError';
  }
}

async function apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    throw new ApiError(`API request failed: ${response.statusText}`, response.status);
  }

  return response.json();
}

export const apiService = {
  isInitialized: true, // Always true for API service

  async initialize(): Promise<void> {
    // No initialization needed for API service
  },

  clearDatabase(): void {
    // Cannot clear server database from client
    throw new Error('Database clearing not supported via API');
  },

  async runInTransaction(callback: () => Promise<void>): Promise<void> {
    // Server-side transactions are handled automatically
    await callback();
  },

  async getSettings(): Promise<Settings> {
    const response = await apiRequest<{ settings: Settings }>('/settings');
    return response.settings;
  },

  async updateSetting(key: keyof Settings, value: string | boolean): Promise<void> {
    await apiRequest(`/settings/${key}`, {
      method: 'PUT',
      body: JSON.stringify({ value }),
    });
  },

  async getTasks(): Promise<Task[]> {
    const response = await apiRequest<{ tasks: Task[] }>('/tasks');
    return response.tasks;
  },

  async addFullTask(task: Task): Promise<void> {
    await apiRequest('/tasks', {
      method: 'POST',
      body: JSON.stringify(task),
    });
  },

  async addTask(task: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>): Promise<Task> {
    const response = await apiRequest<{ task: Task }>('/tasks', {
      method: 'POST',
      body: JSON.stringify(task),
    });
    return response.task;
  },

  async updateTask(task: Task): Promise<void> {
    await apiRequest(`/tasks/${task.id}`, {
      method: 'PUT',
      body: JSON.stringify(task),
    });
  },

  async deleteTask(taskId: string): Promise<void> {
    await apiRequest(`/tasks/${taskId}`, {
      method: 'DELETE',
    });
  },

  async seedInitialTasks(tasksToSeed: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>[]): Promise<void> {
    await apiRequest('/seed', {
      method: 'POST',
      body: JSON.stringify({ tasks: tasksToSeed }),
    });
  }
};