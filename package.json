{"name": "lifetracker", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:client": "vite", "dev:server": "cd server && npm run dev", "build": "npm run build:client && npm run build:server", "build:client": "vite build", "build:server": "cd server && npm install", "start": "cd server && npm start", "preview": "vite preview", "install-all": "npm install && cd server && npm install"}, "dependencies": {"react-dom": "^19.1.0", "react": "^19.1.0"}, "devDependencies": {"@types/node": "^22.14.0", "typescript": "~5.8.2", "vite": "^6.2.0", "concurrently": "^8.2.2"}}