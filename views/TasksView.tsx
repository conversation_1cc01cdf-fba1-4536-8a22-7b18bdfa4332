import React, { useState } from 'react';
import WeatherWidget from '../components/tasks/WeatherWidget';
import TaskList from '../components/tasks/TaskList';
import CalendarWidget from '../components/tasks/CalendarWidget';
import TasksHeader from '../components/tasks/TasksHeader';

const TasksView: React.FC = () => {
    const [sortBy, setSortBy] = useState('due-date');
    const [selectedDate, setSelectedDate] = useState<Date | null>(null);
    const [searchQuery, setSearchQuery] = useState('');

    return (
        <div className="space-y-6">
            <TasksHeader
                selectedDate={selectedDate}
                setSelectedDate={setSelectedDate}
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                sortBy={sortBy}
                setSortBy={setSortBy}
            />
            <div className="lg:grid lg:grid-cols-12 lg:gap-8 space-y-8 lg:space-y-0">
                <div className="lg:col-span-8 xl:col-span-8">
                    <TaskList 
                        sortBy={sortBy}
                        selectedDate={selectedDate}
                        searchQuery={searchQuery}
                    />
                </div>
                <aside className="lg:col-span-4 xl:col-span-4 space-y-8">
                    <CalendarWidget onDateSelect={setSelectedDate} selectedDate={selectedDate} />
                    <WeatherWidget />
                </aside>
            </div>
        </div>
    );
};

export default TasksView;