
import React, { useState } from 'react';

const ProfileView: React.FC = () => {
    const [fullName, setFullName] = useState('<PERSON>');
    const [username, setUsername] = useState('scharway');
    const [feedback, setFeedback] = useState<{ type: 'success' | 'error', message: string } | null>(null);

    const showFeedback = (type: 'success' | 'error', message: string) => {
        setFeedback({ type, message });
        setTimeout(() => setFeedback(null), 4000);
    };

    const handleProfileUpdate = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        // In a real application, you would save the updated `fullName` and `username` here.
        showFeedback('success', 'Profile updated successfully! (This is a mock update)');
    };

    const handleChangePassword = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        const form = e.currentTarget;
        const newPassword = (form.elements.namedItem('new-password') as HTMLInputElement).value;
        const confirmPassword = (form.elements.namedItem('confirm-password') as HTMLInputElement).value;

        if (newPassword.length < 8) {
            showFeedback('error', 'New password must be at least 8 characters long.');
            return;
        }

        if (newPassword !== confirmPassword) {
            showFeedback('error', 'New passwords do not match. Please re-enter.');
            return;
        }

        showFeedback('success', 'Password changed successfully! (This is a mock update)');
        form.reset();
    };
    
    const inputClasses = "block w-full px-3 py-2 rounded-md border-brand-border bg-brand-bg-alt text-brand-primary shadow-sm focus:border-brand-secondary focus:ring-brand-secondary sm:text-sm transition";
    const buttonBaseClasses = "px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-brand-primary hover:bg-brand-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-secondary focus:ring-offset-white";
    const labelClasses = "block text-sm font-medium text-brand-secondary sm:pt-2";

    return (
        <div className="max-w-4xl mx-auto space-y-8">
            {feedback && (
                <div className={`p-4 rounded-md ${feedback.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {feedback.message}
                </div>
            )}

            <div className="space-y-8">
                {/* Account Details */}
                <div className="bg-white rounded-lg shadow-sm border border-brand-border">
                    <form onSubmit={handleProfileUpdate}>
                        <div className="p-6">
                            <h2 className="text-xl font-semibold text-brand-primary border-b border-brand-border pb-4 mb-6">Account Details</h2>
                            <div className="grid grid-cols-1 gap-y-6 sm:grid-cols-3 sm:gap-x-4">
                                <div className="sm:col-span-1">
                                    <label htmlFor="full-name" className={labelClasses}>Full Name</label>
                                </div>
                                <div className="sm:col-span-2">
                                    <input type="text" id="full-name" value={fullName} onChange={e => setFullName(e.target.value)} className={inputClasses} />
                                </div>

                                <div className="sm:col-span-1">
                                    <label htmlFor="username" className={labelClasses}>Username</label>
                                </div>
                                <div className="sm:col-span-2">
                                    <input type="text" id="username" value={username} onChange={e => setUsername(e.target.value)} className={inputClasses} />
                                </div>
                            </div>
                        </div>
                        <div className="bg-gray-50 px-6 py-4 flex justify-end rounded-b-lg border-t border-brand-border">
                            <button type="submit" className={buttonBaseClasses}>Save Profile Changes</button>
                        </div>
                    </form>
                </div>

                {/* Change Password */}
                <div className="bg-white rounded-lg shadow-sm border border-brand-border">
                    <form onSubmit={handleChangePassword}>
                        <div className="p-6">
                             <h2 className="text-xl font-semibold text-brand-primary border-b border-brand-border pb-4 mb-6">Change Password</h2>
                            <div className="grid grid-cols-1 gap-y-6 sm:grid-cols-3 sm:gap-x-4">
                                <div className="sm:col-span-1">
                                    <label htmlFor="current-password" className={labelClasses}>Current Password</label>
                                </div>
                                <div className="sm:col-span-2">
                                    <input type="password" id="current-password" name="current-password" required className={inputClasses} />
                                </div>
                                
                                <div className="sm:col-span-1">
                                    <label htmlFor="new-password" className={labelClasses}>New Password</label>
                                </div>
                                <div className="sm:col-span-2">
                                    <input type="password" id="new-password" name="new-password" required className={inputClasses} />
                                </div>

                                <div className="sm:col-span-1">
                                    <label htmlFor="confirm-password" className={labelClasses}>Confirm New Password</label>
                                </div>
                                <div className="sm:col-span-2">
                                    <input type="password" id="confirm-password" name="confirm-password" required className={inputClasses} />
                                </div>
                            </div>
                        </div>
                        <div className="bg-gray-50 px-6 py-4 flex justify-end rounded-b-lg border-t border-brand-border">
                            <button type="submit" className={buttonBaseClasses}>Update Password</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default ProfileView;