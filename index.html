<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/vite.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>LifeTracker</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
  <style type="text/tailwindcss">
    @layer utilities {
      .custom-scrollbar::-webkit-scrollbar {
        width: 10px;
        height: 10px;
      }
      .custom-scrollbar::-webkit-scrollbar-track {
        background: #eaedf1; 
      }
      .custom-scrollbar::-webkit-scrollbar-thumb {
        background-color: #d4dbe2;
        border-radius: 6px;
        border: 2px solid #eaedf1;
      }
      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background-color: #5c728a;
      }
      .strikethrough-animated {
        position: relative;
        transition: color 0.3s ease-in-out;
      }
      .strikethrough-animated::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        background: currentColor;
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.3s ease-in-out;
      }
      .completed .strikethrough-animated::after {
        transform: scaleX(1);
      }
      @keyframes fade-in {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      .animate-fade-in {
        animation: fade-in 0.3s ease-out forwards;
      }
      @keyframes zoom-in {
        from { transform: scale(0.95); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
      }
      .animate-zoom-in {
        animation: zoom-in 0.3s ease-out forwards;
      }
    }
  </style>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            sans: ['Inter', 'sans-serif'],
          },
          colors: {
            'brand': {
              primary: '#101418',
              secondary: '#5c728a',
              border: '#d4dbe2',
              'bg-hover': '#dce7f3',
              'bg-alt': '#eaedf1',
            },
            rose: {
              50: '#fff1f2',
              100: '#ffe4e6',
              200: '#fecdd3',
              300: '#fda4af',
              400: '#fb7185',
              500: '#f43f5e',
              600: '#e11d48',
              700: '#be123c',
              800: '#9f1239',
              900: '#881337',
            },
          }
        }
      }
    }
  </script>
<script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "sql.js": "https://esm.sh/sql.js@^1.13.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-gray-50">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>